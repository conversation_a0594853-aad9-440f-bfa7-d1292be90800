export default {
  mgtPlat: "MIRU",
  mgtPlatTip: "Система Видеонаблюдения в Реальном Времени",
  scanCodeToLogIn: "Сканируйте код для входа",
  scanCodeTip:
    "Используйте приложение, привязанное к устройству, чтобы отсканировать код для входа",
  qrcodeExpired: "QR-код недействителен",
  username: "Имя пользователя",
  password: "Пароль",
  passwordTip1: "Пожалуйста, введите ваш пароль",
  rememberPwd: "Запомнить пароль",
  pleaseEnterAccount: "Пожалуйста, введите номер вашего аккаунта",
  pleaseEnterCorrectPwd: "Пароль должен содержать от 6 до 128 символов!",
  logIn: "Войти",
  account: "Аккаунт",
  accountTip: "Вводите 6-20 цифр или букв.",
  pleaseEnterCorrectPwdLen: "Длина пароля должна быть не менее 6 символов",
  pleaseEnterCorrectPwd18: "Пароль должен содержать от 6 до 18 символов!",
  verificationCode: "Код подтверждения",
  verificationCodeTip: "Пожалуйста, введите код подтверждения.",
  verificationCodeError: "Ошибка проверочного кода",

  home: "Главная",
  deviceManagement: "Управление устройствами",
  userManagement: "Управление пользователями",
  systemSetting: "Настройки системы",
  operationLogs: "Журналы операций",

  quitTitle: "Выйти",
  quitTip: "Вы уверены, что хотите выйти?",
  submit: "Отправить",
  confirm: "Подтвердить",
  sure: "Подтвердить",
  cancel: "Отмена",
  search: "Поиск",
  inquire: "Поиск",
  nodata: "Нет данных",
  cannotEmpty: "Не может быть пустым",
  operate: "Действие",
  modify: "Редактировать",
  export: "Экспорт",
  edit: "Редактировать",
  delete: "Удалить",
  sureToDelete: "Вы уверены, что хотите удалить?",
  deleteSuccess: "Успешно удалено",
  addSuccess: "Успешно добавлено",
  addFail: "Не удалось добавить",
  remove: "Удалить",
  sureToRemove: "Вы уверены, что хотите удалить?",
  removeSuccess: "Успешно удалено",
  editSuccess: "Успешно отредактировано",
  tip: "Подсказка",
  noMore: "Больше нет",
  getLogSuccess: "Журнал успешно получен",
  all: "Все",
  modifySuccess: "Успешно изменено",
  noData: "Нет данных",
  refresh: "Обновить",
  save: "Сохранить",
  to: "До",
  startDate: "Дата начала",
  endDate: "Дата окончания",
  pleaseSelect: "Пожалуйста, выберите",

  errCode120: "Неизвестная ошибка",
  errCode121: "Ошибка базы данных",
  errCode122: "Время сеанса истекло",
  errCode123: "Ошибка формата сообщения",
  errCode124:
    "Превышен лимит скорости сообщений, пожалуйста, контролируйте разумную скорость потока (100 сообщений в секунду)",
  errCode125: "Неверные параметры",
  errCode126: "Произошла ошибка при вызове API робота",
  errCode127: "Ошибка vid vkey",
  errCode128: "Вызов API робота возвращает ошибку данных",
  errCode129:
    "Не выполнен вход, необходимо войти для выполнения этого действия",
  errCode161: "Неверный аккаунт или пароль",
  errCode200: "CID не существует",
  errCode202: "Псевдоним устройства уже существует",
  errCode203: "Устройство не привязано",
  errCode204: "Устройство уже привязано к другому аккаунту",
  errCode205: "Коды проверки устройства не совпадают",
  errCode206: "Устройство привязано к самому себе",
  errCode214: "Устройство уже привязано к другому приложению",
  errCode215: "Устройство не в сети",
  errCode290: "Случайный код не существует",
  errCode291: "Срок действия случайного кода истек",
  errCode292: "Неверный статус",
  errCode401: "Пожалуйста, войдите снова",
  errCode500: "Ошибка сервера",
  errCode1000: "Ошибка запроса",
  errCode1001: "Ошибка запроса",
  errCode1004: "Сбой подключения",
  errCode1100: "Дубликат названия",
  errCode1101: "Аккаунт уже существует",
  errCode1102: "Текущий пароль неверен",
  errCode2008: "Во время форматирования SD-карты",
  errCode2011: "Не удалось прочитать SD-карту",
  errCode2030: "Историческое видео с SD-карты было прочитано",
  errCode2031: "Не удалось прочитать историческое видео с SD-карты",
  errCode2032: "Не удалось прочитать карту исторического видео с SD-карты",
  timeout: "Сетевая ошибка",
  networkError: "Сетевая ошибка",

  allGroup: "Все группы",
  noDevice: "Нет устройств",
  online: "В сети",
  offline: "Не в сети",
  deviceLoading: "Загрузка списка устройств···",
  videoLoading: "Загрузка видео···",
  failPlay: "Не удалось воспроизвести",
  deviceOffline: "Устройство не в сети",
  noHistoricalVideo: "У устройства нет исторического видео",
  noOssVideo: "У устройства нет облачного хранилища",
  retry: "Повторить",
  incall: "В звонке···",
  screen1: "1-экран",
  screens4: "4-экран",
  screens9: "9-экран",
  screens16: "16-экран",
  screens25: "25-экран",
  liveVideo: "Прямая трансляция",
  cloudStorage: "Облачное хранилище",
  playback: "Воспроизведение",
  todayVideoOver: "Видео на сегодня закончилось",
  movementDetected: "Обнаружено движение",
  soundDetected: "Обнаружен звук",
  humanDetected: "Обнаружен человек",
  noAudioEnabled: "Разрешение на микрофон не включено",
  alarmMessages: "Сообщения тревоги",
  noMessages: "Нет сообщений",
  chooseDevice: "Пожалуйста, выберите устройство",
  exceedMax: "Достигнуто максимальное количество текущих зрителей",
  previousPage: "Предыдущая страница",
  nextPage: "Следующая страница",
  carousel: "Карусель",
  refreshList: "Обновить список",
  refreshListTipCon: "Вы уверены, что хотите обновить список?",
  refreshListTipCon1:
    "Примечание: После обновления списка будет воспроизведен первый экран устройств.",
  refreshSuccess: "Обновлено успешно",
  rebootDevice: "Перезагрузить устройство",
  rebootDeviceTipCon: "Вы уверены, что хотите перезагрузить устройство?",
  scrolledToEnd: "Уже прокручено до конца",
  clickSingleDeviceTip:
    "Состояние карусели не позволяет нажимать на устройство.",

  addGroup: "Добавить группу",
  deviceGroupLoading: "Загрузка списка групп устройств···",
  addDevice: "Добавить устройство",
  strategicAttributes: "Стратегические атрибуты",
  bulkDelete: "Массовое удаление",
  status: "Статус",
  cid: "CID устройства",
  versionNumber: "Номер версии",
  deviceNickname: "Псевдоним устройства",
  group: "Группа",
  strategy: "Стратегия",
  editGroup: "Редактировать группу",
  groupName: "Название группы",
  groupNumber: "Номер группы",
  authorizedUser: "Авторизованный пользователь",
  groupNameLimit: "Ограничение длины 20 символов",
  delGroup: "Удалить группу",
  editDevice: "Редактировать устройство",
  sn: "CID устройства",
  snEnter: "Пожалуйста, введите CID",
  snEnterTip: "Пожалуйста, введите действительный 12-значный код CID",
  deviceVerificationCode: "Код проверки устройства",
  deviceCodeEnter: "Пожалуйста, введите код проверки",
  deviceNameEnter: "Пожалуйста, введите псевдоним устройства",
  deleteDevice: "Удалить устройство",
  bulkDelDeviceTip:
    "Пожалуйста, отметьте устройства, которые вы хотите удалить",
  device: "Устройство",
  deviceSearchTip: "CID устройства/Псевдоним устройства",
  delGroupHasDeviceTip: "В группе есть устройства, поэтому её нельзя удалить",

  nickName: "Псевдоним",
  mark: "Отметка",
  addUser: "Добавить пользователя",
  resetPassword: "Сбросить пароль",
  inputLengthLimit: "Ограничение длины {limit} символов",
  editUser: "Редактировать пользователя",
  deleteUser: "Удалить пользователя",
  sureToReset: `Вы уверены, что хотите сбросить пароль на "123456"?`,
  bulkDelUserTip:
    "Пожалуйста, отметьте пользователей, которых вы хотите удалить",
  userLimitTip: "Ограничение на 5 пользователей",

  modifyPassword: "Изменить пароль",
  passwordNotMatching: "Пароли не совпадают",
  oldPassword: "Текущий пароль",
  newPassword: "Новый пароль",
  checkPassword: "Подтвердите пароль",
  passwordSame: "Новый пароль совпадает со старым паролем",

  operationTime: "Время операции",
  operationType: "Тип операции",
  editDeviceGroup: "Редактировать группу устройства",
  operationContent: "Содержание операции",

  saveVideo: "Сохранить видео",
  time: "время",
  startTime: "Время начала",
  endTime: "Время окончания",
  empty: "Очистить",
  timerangeexceeds:
    "Выбранный диапазон превышает ограничение. Пожалуйста, удалите видео в управлении видео или выберите заново!",
  novideo: "В выбранном временном диапазоне видео не найдено, выберите заново!",
  videomanagement: "Управление видео",
  selectgroup: "Пожалуйста, выберите группу",
  allselect: "Выделить все",
  download: "Скачать",
  downloading: "Загрузка...",
  downloadSuccess: "Загрузка успешна",
  downloadFailed: "Загрузка не удалась",
  saveVideoSuccess: "Сохранить видео успешно"
};
