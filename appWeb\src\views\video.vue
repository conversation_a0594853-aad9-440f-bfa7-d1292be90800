<template>
  <div class="webRTCVideo">
    <div class="deviceBox">
      <div v-if="group_list.length > 0" class="deviceGroupBox">
        <el-dropdown
          trigger="click"
          class="selectGroupBox"
          @command="filterDeviceList"
          @visible-change="groupListVisibleChange"
        >
          <span class="selectedGroupName pointer">
            <span class="lineClamp3">{{ selected_group_label }}</span>
            <span class="bottomArrowBox"
              ><img
                v-if="group_dropdown_visible"
                class="bottomArrowIcon"
                src="../assets/img/topArrow.png" /><img
                v-else
                class="bottomArrowIcon"
                src="../assets/img/bottomArrow.png"
            /></span>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in group_list"
              :key="item.group_id"
              :command="item.group_id"
              :disabled="selected_group === item.group_id"
              >{{ item.alias }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <div class="loadImg pointer" @click="showDialog('refresh')"></div>
      </div>
      <div class="deviceListCon">
        <div v-if="device_loading" class="deviceLoadingBox">
          <img class="loadingIcon" src="../assets/img/loading.png" />
          <div>{{ $t("deviceLoading") }}</div>
        </div>
        <div v-else-if="filter_device_list.length === 0" class="noDeviceBox">
          <img class="noDeviceIcon" src="../assets/img/noDataLogo.png" />
          <span>{{ $t("noDevice") }}</span>
        </div>
        <div
          v-for="(item, index) in filter_device_list"
          v-else
          :key="index"
          class="deviceItem pointer"
          :class="{
            selectedDeviceVideo: item.selected_device_flag,
            selectedSingleDeviceVideo: item.selected_single_device_flag,
          }"
          @click.stop="selectSingleDevice(item)"
        >
          <div class="deviceItemLeft">
            <img class="deviceIcon" src="../assets/img/deviceIcon.png" />
            <span
              :title="item.alias ? item.alias : item.sn"
              class="lineClamp2"
              v-text="item.alias ? item.alias : item.sn"
            />
          </div>
          <span :class="item.net > 0 ? 'onDot' : 'offDot'" class="dot" />
        </div>
      </div>
    </div>
    <div class="playVideoBox">
      <div class="liveModeBox">
        <div
          class="videoBoxOutter"
          ref="videoBoxOutter"
          @click="onClickVideoBoxOutter"
        >
          <div
            :class="fulling ? 'fullingBox' : ''"
            class="videoBox ossVideoBox"
          >
            <div
              v-for="(item, index) in video_list"
              :class="{
                videoItem1: multi_screen === 1,
                videoItem4: multi_screen === 4,
                videoItem9: multi_screen === 9,
                videoItem16: multi_screen === 16,
                videoItem25: multi_screen === 25,
                selectedSingleDeviceVideoBox: item.selected_single_device_flag,
              }"
              :key="index"
              class="videoItem"
              @mouseover="onMouseEnterVideoItem(item)"
              @mouseleave="ouMouseLeaveVideoItem(item)"
              @click.stop="handleSelectedSingleDevice(item)"
            >
              <!-- channel name:<input v-model="item.channel_name"></input>
                <el-button id="viewer-button" type="primary" :disabled="item.live_stream" @click="toStartViewer(index)">Start Viewer</el-button> -->
              <div :ref="item.video_con_ref" class="video-container">
                <!-- 视频loading -->
                <div v-if="item.remote_stream_loading" class="videoLoadingBox">
                  <img class="loadingIcon" src="../assets/img/loading.png" />
                  <div>{{ $t("videoLoading") }}</div>
                </div>
                <!-- 播放失败，重试 -->
                <div v-else-if="item.play_state === 2" class="videoFailBox">
                  <img class="playFailIcon" src="../assets/img/playFail.png" />
                  <div>
                    <div>{{ $t("failPlay") }}</div>
                    <div
                      class="retryBtn pointer"
                      @click.stop="toPlayFlvVideo(index, item.device_index)"
                    >
                      {{ $t("retry") }}
                    </div>
                  </div>
                </div>
                <!-- 设备离线 -->
                <div v-else-if="item.play_state === 3" class="videoFailBox">
                  <img class="playFailIcon" src="../assets/img/deviceOff.png" />
                  <div>{{ $t("deviceOffline") }}</div>
                </div>
                <video
                  v-show="item.flvPlayer_flag"
                  :ref="item.video_ref"
                  class="remote-view"
                  autoplay
                  disablePictureInPicture
                />
                <!-- <video class="remote-view" :ref="item.video_ref" autoplay playsinline disablePictureInPicture muted/> -->
                <!-- 悬浮头部 -->
                <div
                  v-show="item.sn && item.show_headwrap_operation"
                  class="videoWrapHead"
                >
                  <div class="videoWrapHeadLeft text-nowrap">
                    <span
                      class="deviceName text-nowrap"
                      v-text="item.alias ? item.alias : item.sn"
                    />
                    <span
                      v-if="item.net > 0"
                      class="deviceNetStatus deviceOnline"
                      >{{ $t("online") }}</span
                    >
                    <span v-else class="deviceNetStatus deviceOffline">{{
                      $t("offline")
                    }}</span>
                  </div>
                  <div class="videoWrapHeadRight">
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="$t('saveVideo')"
                      placement="top"
                    >
                      <div
                        class="saveVideoIcon pointer"
                        @click.stop="showSaveVideoPop(item)"
                      ></div>
                    </el-tooltip>

                    <div
                      class="stopVideoImg pointer"
                      @click.stop="showDialog('reboot', item)"
                    ></div>
                  </div>
                </div>
                <div
                  v-if="item.show_PTZ_status && item.show_headwrap_operation"
                  class="showPtzBox"
                  @click.stop
                >
                  <div class="videoWrapDirection">
                    <div class="directionBtnBox">
                      <!-- 0-停止 1-上 2-下 3-左 4-右 -->
                      <div
                        class="directionBtn directionLeftBtn"
                        @mousedown="direction(3, item.channel_name)"
                        @mouseup="stopDirection(item.channel_name)"
                      />
                      <div
                        class="directionBtn directionUpBtn"
                        @mousedown="direction(1, item.channel_name)"
                        @mouseup="stopDirection(item.channel_name)"
                      />
                      <div
                        class="directionBtn directionDownBtn"
                        @mousedown="direction(2, item.channel_name)"
                        @mouseup="stopDirection(item.channel_name)"
                      />
                      <div
                        class="directionBtn directionRightBtn"
                        @mousedown="direction(4, item.channel_name)"
                        @mouseup="stopDirection(item.channel_name)"
                      />
                    </div>
                  </div>
                </div>
                <div
                  v-show="item.flvPlayer_flag && item.show_headwrap_operation"
                  class="cameraControl"
                  @click.stop
                >
                  <!-- 声音 -->
                  <div
                    v-if="item.volume === 0"
                    class="volumeIcon noVolumeIcon pointer"
                    @click.stop="unmute(item)"
                  ></div>
                  <div
                    v-else
                    class="volumeIcon hasVolumeIcon pointer"
                    @click.stop="mute(item)"
                  ></div>
                  <el-slider
                    v-show="item.volume === 0"
                    class="sliderStyle"
                    :value="0"
                    :show-tooltip="false"
                    disabled
                  ></el-slider>
                  <el-slider
                    v-show="item.volume !== 0"
                    class="sliderStyle"
                    v-model="item.volume"
                    :show-tooltip="false"
                    @change="dragChangeVolume(item)"
                  ></el-slider>
                  <!-- 云台图标 -->
                  <img
                    v-if="item.show_PTZ_status"
                    class="ptzIcon pointer"
                    src="../assets/img/ptzClick.png"
                    @click.stop="showPTZ(item, false)"
                  />
                  <div
                    v-if="account_type === '1' && !item.show_PTZ_status"
                    class="ptzIcon showPtzIcon pointer"
                    @click.stop="showPTZ(item, true)"
                  ></div>
                  <!-- 全屏单个视频 -->
                  <div
                    v-if="!fulling"
                    class="fullingSingleVideoBox"
                    @click.stop="
                      fulling_single_video
                        ? exitFullscreen()
                        : fullSingleScreen(item)
                    "
                  >
                    <div
                      v-if="fulling_single_video"
                      class="exitFullSingleScreenIcon pointer"
                    ></div>
                    <div v-else class="fullScreenSingleIcon pointer"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="isShowModeFooter"
            :class="fulling ? 'fullingBox' : ''"
            class="viderModeFooter"
            @mouseover="onMouseEnterFooterHolder"
            @mouseleave="ouMouseLiveFooter"
          >
            <!-- 多屏 -->
            <div class="multiScreenBox">
              <div
                :class="{ selectedMultiScreen: multi_screen === 1 }"
                class="multiScreenItem"
                @click.stop="changeVideoBox(1)"
              >
                {{ $t("screen1") }}
              </div>
              <div
                :class="{ selectedMultiScreen: multi_screen === 4 }"
                class="multiScreenItem"
                @click.stop="changeVideoBox(4)"
              >
                {{ $t("screens4") }}
              </div>
              <div
                :class="{ selectedMultiScreen: multi_screen === 9 }"
                class="multiScreenItem"
                @click.stop="changeVideoBox(9)"
              >
                {{ $t("screens9") }}
              </div>
              <div
                :class="{ selectedMultiScreen: multi_screen === 16 }"
                class="multiScreenItem"
                @click.stop="changeVideoBox(16)"
              >
                {{ $t("screens16") }}
              </div>
              <div
                :class="{ selectedMultiScreen: multi_screen === 25 }"
                class="multiScreenItem"
                @click.stop="changeVideoBox(25)"
              >
                {{ $t("screens25") }}
              </div>
            </div>
            <!-- 分页 -->
            <div class="pageChangeBox">
              <div
                class="multiScreenItem"
                @click.stop="changePreviousPage()"
                @mousedown="onHoverPrev(true)"
                @mouseup="onHoverPrev(false)"
              >
                <img
                  v-if="isHoveringPrev"
                  src="../assets/img/arrow_prev_click.png"
                  alt=""
                />
                <img v-else src="../assets/img/arrow_prev.png" alt="" />
              </div>
              <div
                class="multiScreenItem"
                @click.stop="changeNextPage()"
                @mousedown="onHoverNext(true)"
                @mouseup="onHoverNext(false)"
              >
                <img
                  v-if="isHoveringNext"
                  src="../assets/img/arrow_next_click.png"
                  alt=""
                />
                <img v-else src="../assets/img/arrow_next.png" alt="" />
              </div>
            </div>
            <!-- 轮播 -->
            <div class="carouselBox" @click.stop>
              <el-switch
                v-model="carousel_switch"
                active-color="var(--color-primary)"
                inactive-color="var(--color-neutral-500)"
                @change="checkCarouselSwitch()"
              ></el-switch>
              <span class="carouselText">{{ $t("carousel") }}</span>
            </div>
            <div style="flex: 1"></div>
            <!-- 全屏 -->
            <div
              :class="{ fullingBox: fulling }"
              class="rightControlBox"
              @click.stop="fulling ? exitFullscreen() : fullScreen()"
            >
              <div v-if="fulling" class="exitFullScreenIcon pointer"></div>
              <div v-else class="fullScreenIcon pointer"></div>
            </div>
          </div>
          <div
            v-if="fulling"
            v-show="!isShowModeFooter"
            class="modeFooterHolder"
            @mouseenter="onMouseEnterFooterHolder"
          ></div>
          <div
            v-if="fulling || fulling_single_video"
            v-show="dialog_visible || dialogSaveVideo_visible"
            class="tipDialogMask"
          ></div>
        </div>
        <div style="display: none">
          <div class="video-container">
            <video
              ref="localView"
              class="local-view"
              autoplay
              playsinline
              controls
              disablePictureInPicture
            />
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialog_title"
      class="tipDialog"
      width="340px"
      :visible.sync="dialog_visible"
      :append-to-body="false"
      @close="closeDialog()"
    >
      <section>
        <div class="dialogCont">
          <div>{{ dialog_content }}</div>
          <div>{{ dialog_tip }}</div>
        </div>
      </section>
      <span slot="footer" class="dialog-footer">
        <el-button
          :loading="submit_loading"
          type="primary"
          class="tipSubmitBtn"
          @click.stop="submitDialog()"
          >{{ $t("confirm") }}</el-button
        >
        <el-button
          type="info"
          class="tipCancelBtn"
          @click.stop="closeDialog()"
          >{{ $t("cancel") }}</el-button
        >
      </span>
    </el-dialog>

    <!-- 保存视频弹窗 -->
    <el-dialog
      :title="saveVideo_title"
      class="saveVideoDialog"
      width="512px"
      :visible.sync="dialogSaveVideo_visible"
      :append-to-body="false"
      @close="closeDialog()"
    >
      <div class="saveVideoBox">
        <div class="saveVideoTitle">{{ $t("time") }}</div>
        <div class="datePickerBox">
          <el-date-picker
            v-model="saveVideoBeginTime"
            style="width: 200px"
            type="datetime"
            :placeholder="$t('startTime')"
            format="yyyy-MM-dd HH:mm"
            :picker-options="pickerOptionsBegin"
            @change="onBeginTimeChange"
          >
          </el-date-picker>
          <span>~</span>
          <el-date-picker
            v-model="saveVideoEndTime"
            style="width: 200px"
            type="datetime"
            :placeholder="$t('endTime')"
            format="yyyy-MM-dd HH:mm"
            :picker-options="pickerOptionsEnd"
            @change="onEndTimeChange"
          >
          </el-date-picker>
        </div>
        <div style="flex: 1"></div>
        <span slot="footer" class="dialog-footer">
          <el-button class="tipCancelBtn" @click.capture="closeDialog()">{{
            $t("cancel")
          }}</el-button>
          <el-button
            :loading="isSubmitting"
            class="tipSubmitBtn"
            @click.capture="submitSaveVideo"
            >{{ $t("sure") }}</el-button
          >
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDeviceGroupList, getDeviceList as getDevices } from '@/api/deviceMgt'
import { decryptionKey } from '@/utils/jiami'
import { setToken } from '@/utils/auth'
import mpegts from '@/utils/mpegts.js'
import CylanRtcSession from '@/utils/cylan-webrtc.js'
import { startViewer, stopViewer } from '@/utils/viewer'
import { formatTime, handleMins, binaryAddZero } from '@/utils/formatDate'
import { getDeviceVideo } from '@/api/videoMgt'

let clickVideoBoxOutterResumeTimeout = null
export default {
  name: 'Video',
  components: {
  },
  data () {
    return {
      account_type: sessionStorage.account_type,
      group_dropdown_visible: false,
      group_list: [],
      selected_group: '',
      selected_group_label: '',
      device_loading: true,
      all_device_list: [],
      filter_device_list: [],
      video_list: [],
      multi_screen: 25, // 多屏数
      single_selected_device: {},
      single_selected_video: {},
      viewer: [],
      fulling: false, // 被选中的视频是否是全屏状态

      // supportAspectRatio: false,

      flvPlayer: [],
      flv_heartbeat_time: 10000, // 给flv视频发的心跳的时长
      flv_loading_time: 10000, // 视频loading的时长，没加载出来就显示失败重试

      carousel_switch: false, // 轮播开关
      carousel_time: 10000,
      carousel_interval: null,
      page_num: 1,

      dialog_title: '',
      dialog_visible: false,
      dialog_content: '',
      dialog_tip: '',
      dialog_type: '',
      reboot_device: {}, // 需要重启的设备
      submit_loading: false,

      isShowModeFooterWithClick: false,
      isShowModeFooterWithMouse: false,

      isHoveringPrev: false,
      isHoveringNext: false,

      fulling_single_video: false, // 单个视频画面全屏

      session_video_url: JSON.parse(sessionStorage.videoUrl),

      saveVideo_title: this.$t('saveVideo'),
      dialogSaveVideo_visible: false,
      saveVideoBeginTime: '',
      saveVideoEndTime: new Date(),
      isSubmitting: false,

      saveVideoSn: '',
      pickerOptionsBegin: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        },
        selectableRange: '00:00:00 - 00:00:00'
      },
      pickerOptionsEnd: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        },
        selectableRange: '00:00:00 - 00:00:00'
      }
    }
  },
  computed: {
    isShowModeFooter () {
      return (this.fulling && (this.isShowModeFooterWithClick || this.isShowModeFooterWithMouse)) || !this.fulling
    }
  },
  watch: {

  },
  created () {
    this.video_list = [].concat(Array(this.multi_screen).fill({}))
    console.log(window.location.protocol)
    console.log('sessionStorage.home：' + sessionStorage.home)
    if (sessionStorage.home && sessionStorage.home === '1') {
      console.log('刷新')
      if (this.$websocket) {
        this.$websocket.closeWebsocket()
      }
      // 刷新界面websocket断开重连
      if (sessionStorage.username && sessionStorage.username !== '') {
        this.$websocket.initWebSocket('Reconnection')
        this.$bus.$once('cli_miru_login_rsp', response => {
          this.$store.commit('SET_TOKEN', response.body.sessid)
          setToken(response.body.sessid)
          this.getDeviceList()
        })
      }
    } else {
      console.log('登录进来')
      // 要判断websocket是否连接状态
      this.checkWebsocket()
    }
    // 监听当前页全屏、退出全屏事件
    this.$nextTick(() => {
      const fullArray = ['fullscreenchange', 'mozfullscreenchange', 'webkitfullscreenchange', 'msfullscreenchange']
      fullArray.forEach(item => {
        window.addEventListener(item, () => this.fullScreenChange())
      })
    })
  },
  mounted () {
    sessionStorage.home = '1'
    // 不支持aspect-ratio的浏览器用兼容方案
    // if (this.isPropertySupported('aspect-ratio')) {
    //   console.log('支持aspect-ratio')
    //   this.supportAspectRatio = true
    // } else {
    //   console.log('不支持aspect-ratio')
    //   this.supportAspectRatio = false
    //   this.resizeVideoBox()
    //   this.resizePtzBox()
    //   const _this = this
    //   window.addEventListener('resize', this.debounce(function (event) {
    //     _this.resizeVideoBox()
    //     _this.resizePtzBox()
    //   }, 300))
    // }
    // 云台转到底了
    this.$bus.$on('pub_dp_act', response => {
      if (response.body.dp_list[0].id === 539) {
        this.$message.warning(this.$t('scrolledToEnd'))
      }
    })
    // 接收推送过来的设备在线离线消息
    this.$bus.$on('pub_dp_push_data', response => {
      console.log('pub_dp_push_data')
      let sn = response.headers.caller
      // DP201消息
      if (response.body.length > 0 && response.body[0].id === 201) {
        let net = JSON.parse(response.body[0]['value']).net
        let net_status = net <= 0 ? this.$t('offline') : this.$t('online')
        // 刷新设备列表里设备的网络状态
        this.group_list.map(item => {
          if (item.sn_list.length > 0) {
            item.sn_list.map(i => {
              if (sn === i.sn) {
                this.$set(i, 'net', net)
                this.$set(i, 'net_status', net_status)
              }
            })
          }
        })
        // 刷新视频列表里设备的网络状态
        this.video_list.map((item, index) => {
          if (sn === item.sn) {
            this.$set(item, 'net', net)
            this.$set(item, 'net_status', net_status)
            // 设备离线，断开视频
            if (net <= 0) {
              this.toStopFlvVideo(index)
              this.video_list[index].remote_stream_loading = false
              this.video_list[index].flvPlayer_flag = false
              this.video_list[index].play_state = 3 // 设备离线
            } else {
              // 视频列表里的设备上线，如果没有开轮播，就给它开始播放视频
              if (!this.carousel_switch) {
                this.toPlayFlvVideo(index, item.device_index)
              }
            }
          }
        })
      }
    })
  },
  beforeDestroy () {
    // 要销毁当前页播放的
    for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
      if (this.filter_device_list[i]) {
        this.$set(this.filter_device_list[i], 'selected_device_flag', false)
        this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
        this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
      }
    }
    this.single_selected_device = {}
    this.video_list = []
    // 关轮播定时器
    this.endCarousel()
    this.$bus.$off('pub_dp_act')
    this.$bus.$off('pub_dp_push_data')
    console.log('beforeDestroy')
  },
  methods: {
    isPropertySupported (property) {
      return property in document.body.style
    },
    checkWebsocket () {
      // 0 - 表示连接尚未建立，1 - 表示连接已建立，可以进行通信，2 - 表示连接正在进行关闭，3 - 表示连接已经关闭或者连接不能打开
      console.log(this.$websocket)
      var state = this.$websocket.socket.readyState
      console.log(state)
      if (state === 0) {
        setTimeout(() => {
          this.checkWebsocket()
        }, 300)
      } else if (state === 1) {
        this.getDeviceList()
      }
    },
    getDeviceList (type) {
      this.device_loading = true

      this.group_list = []
      getDeviceGroupList()
        .then(res => {
          if (res.code === 200) {
            if (res.datas.length > 0) {
              res.datas && res.datas.map((item, index) => {
                this.group_list.push({
                  group_id: item.group_id,
                  alias: item.name
                })
              })
            }
          }
        }).catch(err => {
          this.device_loading = false
        }).finally(() => {
          if (this.group_list.length === 0) {
            this.device_loading = false
            return
          }
          let reqData = {
            offset: 0,
            limit: 1000
          }
          console.log(this.group_list)
          getDevices(reqData)
            .then(res => {
              if (res.code === 200) {
                const list = res.datas.map(i => {
                  let gp = this.group_list.find(g => g.alias === i.group_name)
                  return {
                    sn: i.cid,
                    net: i.net,
                    net_status: i.net <= 0 ? this.$t('offline') : this.$t('online'),
                    version: i.version,
                    alias: i.name,
                    group_name: i.group_name,
                    group_id: gp ? gp.group_id : '-1'
                  }
                })

                const _groupList = [...this.group_list]
                _groupList.forEach(i => {
                  i.sn_list = list.filter(item => item.group_id === i.group_id).map((item, index) => ({ ...item, device_index: index }))
                })
                this.group_list = _groupList


                // 刷新组设备列表时
                if (type && type === 'refresh') {
                  console.log('刷新组设备列表')
                  this.$message.success(this.$t('refreshSuccess'));
                  this.filterDeviceList(this.selected_group, 'refresh')
                } else {
                  console.log('刚进入页面')
                  if (this.group_list.length === 0) {
                    return
                  }
                  this.selected_group = this.group_list[0].group_id
                  this.selected_group_label = this.group_list[0].alias
                  this.filter_device_list = this.group_list[0].sn_list

                  this.video_list = [].concat(Array(this.multi_screen).fill({}))
                  // 进入页面就开始播放第一页的设备
                  if (this.filter_device_list.length > 0) {
                    for (let j = 0; j < this.multi_screen; j++) {
                      if (this.filter_device_list[j]) {
                        this.filter_device_list[j].selected_device_flag = true
                        this.filter_device_list[j].selected_single_device_flag = false
                      }
                      let temp_obj = {
                        channel_name: this.filter_device_list[j] ? this.filter_device_list[j].sn : '',
                        video_ref: "remoteView" + j,
                        video_con_ref: "videoContainer" + j,
                        video_index: j,
                        live_stream: false,
                        volume: 0,
                        show_PTZ_status: false,
                        show_headwrap_operation: false,
                        remote_stream_loading: false
                      }
                      if (this.filter_device_list[j] && this.filter_device_list[j].net <= 0) {
                        temp_obj.flv_heartbeat = null // 播放的心跳
                        temp_obj.play_state = 3 // 设备离线
                      }
                      Object.assign(temp_obj, this.filter_device_list[j])
                      this.$set(this.video_list, j, temp_obj)
                      if (this.filter_device_list[j] && this.filter_device_list[j].net > 0) {
                        this.$nextTick(() => {
                          this.toPlayFlvVideo(j, j)
                        })
                      }
                    }
                  }
                }
                this.single_selected_device = {}
                this.device_loading = false
                console.log(this.filter_device_list)
                console.log('video_list: ', this.video_list)
              }
            }).catch(err => {

            }).finally(() => {
            })
        })
      return
      var post_data = {
        'time': 0,
        'type': 0
      }
      this.$websocket.webSocketSend('cli_dev_list', post_data)
      this.$bus.$once('cli_dev_list_rsp', response => {
        if (typeof (response) === 'number') {
          this.device_loading = false
          return
        }
        var list = response.body.list
        this.group_list = list
        this.all_device_list = []
        console.log(list)
        if (list && list.length > 0) {
          list.map((item, index) => {
            if (item.sn_list.length > 0) {
              item.sn_list.map(obj => {
                obj.group_id = item.group_id
                obj.group_name = item.alias
                obj.net = 1
              })
            }
          })

        }
        // 刷新组设备列表时
        if (type && type === 'refresh') {
          console.log('刷新组设备列表')
          this.$message.success(this.$t('refreshSuccess'));
          this.filterDeviceList(this.selected_group, 'refresh')
        } else {
          console.log('刚进入页面')
          if (this.group_list.length === 0) {
            return
          }
          this.selected_group = this.group_list[0].group_id
          this.selected_group_label = this.group_list[0].alias
          this.filter_device_list = this.group_list[0].sn_list

          this.video_list = [].concat(Array(this.multi_screen).fill({}))
          // 进入页面就开始播放第一页的设备
          if (this.filter_device_list.length > 0) {
            for (let j = 0; j < this.multi_screen; j++) {
              if (this.filter_device_list[j]) {
                this.filter_device_list[j].selected_device_flag = true
                this.filter_device_list[j].selected_single_device_flag = false
              }
              let temp_obj = {
                channel_name: this.filter_device_list[j] ? this.filter_device_list[j].sn : '',
                video_ref: "remoteView" + j,
                video_con_ref: "videoContainer" + j,
                video_index: j,
                live_stream: false,
                volume: 0,
                show_PTZ_status: false,
                show_headwrap_operation: false,
                remote_stream_loading: false
              }
              if (this.filter_device_list[j] && this.filter_device_list[j].net <= 0) {
                temp_obj.flv_heartbeat = null // 播放的心跳
                temp_obj.play_state = 3 // 设备离线
              }
              Object.assign(temp_obj, this.filter_device_list[j])
              this.$set(this.video_list, j, temp_obj)
              if (this.filter_device_list[j] && this.filter_device_list[j].net > 0) {
                this.$nextTick(() => {
                  this.toPlayFlvVideo(j, j)
                })
              }
            }
          }
        }
        this.device_loading = false
        console.log(this.filter_device_list)
        console.log('video_list: ', this.video_list)

      })
    },
    // 组列表的下拉框出现/隐藏时触发 用来切换图标
    groupListVisibleChange (val) {
      this.group_dropdown_visible = val
    },
    // 组的设备列表
    filterDeviceList (id, type) {
      if (type === 'refresh') {
        console.log('刷新分组')
      } else {
        console.log('选择分组')
        if (this.selected_group === id) {
          return
        }
      }
      this.device_loading = true
      // 先把之前选中的设备样式去掉
      const old_device_list = this.filter_device_list
      if (old_device_list.length > 0) {
        old_device_list.map(item => {
          this.$set(item, 'selected_device_flag', false)
          this.$set(item, 'selected_single_device_flag', false)
        })
      }

      // 停止所有播放的设备
      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        if (this.filter_device_list[i]) {
          this.$set(this.filter_device_list[i], 'selected_device_flag', false)
          this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
          this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
        }
      }
      this.single_selected_device = {}
      this.video_list = [].concat(Array(this.multi_screen).fill({}))

      // 更新设备列表
      this.selected_group = id
      let group_index = -1
      this.group_list.map((m, index) => {
        if (m.group_id === id) {
          this.selected_group_label = m.alias
          group_index = index
        }
      })
      this.filter_device_list = this.group_list[group_index].sn_list


      this.flvPlayer = []
      this.page_num = 1

      // 检查轮播
      this.checkCarouselSwitch()

      // 设备列表处理好了，自动播放第一页在线的设备
      if (this.filter_device_list.length > 0) {
        for (let j = 0; j < this.multi_screen; j++) {
          if (this.filter_device_list[j]) {
            this.filter_device_list[j].selected_device_flag = true
            this.filter_device_list[j].selected_single_device_flag = false
          }
          let temp_obj = {
            channel_name: this.filter_device_list[j] ? this.filter_device_list[j].sn : '',
            video_ref: "remoteView" + j,
            video_con_ref: "videoContainer" + j,
            video_index: j,
            live_stream: false,
            volume: 0,
            show_PTZ_status: false,
            show_headwrap_operation: false,
            remote_stream_loading: false
          }
          if (this.filter_device_list[j] && this.filter_device_list[j].net <= 0) {
            temp_obj.flv_heartbeat = null // 播放的心跳
            temp_obj.play_state = 3 // 设备离线
          }
          Object.assign(temp_obj, this.filter_device_list[j])
          this.$set(this.video_list, j, temp_obj)
          if (this.filter_device_list[j] && this.filter_device_list[j].net > 0) {
            this.$nextTick(() => {
              this.toPlayFlvVideo(j, j)
            })
          }
        }
      }
      this.device_loading = false
      console.log('video_list: ', this.video_list)
    },
    resizeVideoBox () {
      const eleArr = document.getElementsByClassName('video-container')
      const boxWidth = document.getElementsByClassName('video-container')[0].offsetWidth
      for (let i = 0; i < eleArr.length; i++) {
        eleArr[i].style.height = (boxWidth * 9 / 16).toFixed(2) + 'px'
      }
    },
    resizePtzBox () {
      const eleArr = document.getElementsByClassName('showPtzBox')
      const boxWidth = document.getElementsByClassName('showPtzBox')[0].offsetWidth
      for (let i = 0; i < eleArr.length; i++) {
        eleArr[i].style.height = boxWidth + 'px'
      }
    },
    // window.resize 防抖操作
    debounce (fn, delay) {
      let time = null
      let timer = null
      let newTime = null
      function task () {
        newTime = +new Date()
        if (newTime - time < delay) {
          timer = setTimeout(task, delay)
        } else {
          fn()
          timer = null
        }
        time = newTime
      }
      return function () {
        // 更新时间戳
        time = +new Date()
        if (!timer) {
          timer = setTimeout(task, delay)
        }
      }
    },
    // 多屏切换
    changeVideoBox (num) {
      console.log('多屏切换')
      // 要销毁当前页播放的
      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        if (this.filter_device_list[i]) {
          this.$set(this.filter_device_list[i], 'selected_device_flag', false)
          this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
          this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
        }
      }

      this.multi_screen = num
      this.page_num = 1
      this.video_list = [].concat(Array(this.multi_screen).fill({}))
      this.single_selected_device = {}

      // 检查轮播
      this.checkCarouselSwitch()

      // 自动播放第一页在线的设备
      if (this.filter_device_list.length > 0) {
        for (let j = 0; j < this.multi_screen; j++) {
          if (this.filter_device_list[j]) {
            this.filter_device_list[j].selected_device_flag = true
            this.filter_device_list[j].selected_single_device_flag = false
          }
          let temp_obj = {
            channel_name: this.filter_device_list[j] ? this.filter_device_list[j].sn : '',
            video_ref: "remoteView" + j,
            video_con_ref: "videoContainer" + j,
            video_index: j,
            live_stream: false,
            volume: 0,
            show_PTZ_status: false,
            show_headwrap_operation: false,
            remote_stream_loading: false
          }
          if (this.filter_device_list[j] && this.filter_device_list[j].net <= 0) {
            temp_obj.flv_heartbeat = null // 播放的心跳
            temp_obj.play_state = 3 // 设备离线
          }
          Object.assign(temp_obj, this.filter_device_list[j])
          this.$set(this.video_list, j, temp_obj)
          if (this.filter_device_list[j] && this.filter_device_list[j].net > 0) {
            this.$nextTick(() => {
              this.toPlayFlvVideo(j, j)
            })
          }
        }
      }
      setTimeout(() => {
        this.resizeVideoBox()
      }, 10);

      // 不支持aspect-ratio的浏览器多屏切换时也要调用兼容方案
      // if (!this.supportAspectRatio) {
      //   setTimeout(() => {
      //     this.resizePtzBox()
      //   }, 1)
      // }
    },
    // 云台控制 0-停止 1-上 2-下 3-左 4-右
    direction (val, cid) {
      var _this = this
      var post_data = {
        'act': 1,
        'dp_list': [
          {
            id: 537,
            time: Date.parse(new Date()),
            value: JSON.stringify({ 'direction': val })
          }
        ]
      }
      this.$websocket.webSocketSend('pub_dp_act', post_data, cid)
    },
    stopDirection (cid) {
      var post_data = {
        'act': 1,
        'dp_list': [
          {
            id: 537,
            time: Date.parse(new Date()),
            value: JSON.stringify({ 'direction': 0 })
          }
        ]
      }
      this.$websocket.webSocketSend('pub_dp_act', post_data, cid)
    },
    // 全屏事件
    fullScreen () {

      /* 全屏指定元素的代码 */
      const videoBoxElement = this.$refs['videoBoxOutter']
      if (videoBoxElement.requestFullscreen) {
        videoBoxElement.requestFullscreen();
      } else if (element.mozRequestFullScreen) { // Firefox
        videoBoxElement.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) { // Chrome, Safari, and Opera
        videoBoxElement.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) { // IE/Edge
        videoBoxElement.msRequestFullscreen();
      }

      return

      var index = this.single_selected_video.index
      console.log(this.video_list[index].live_stream)
      // 播放失败/离线 的界面,禁用全屏
      if (!this.video_list[index].live_stream) {
        return
      }
      // var el = this.$refs[this.single_selected_video.video_ref][0];
      // var el = document.getElementById('app')
      if (!this.single_selected_video.video_con_ref) {
        return
      }
      var el = this.$refs[this.single_selected_video.video_con_ref][0]
      var rfs = el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullScreen
      if (typeof rfs !== 'undefined' && rfs) {
        rfs.call(el)
      } else if (typeof window.ActiveXObject !== 'undefined') {
        // for Internet Explorer
        var wscript = new ActiveXObject('WScript.Shell')
        if (wscript !== null) {
          wscript.SendKeys('{F11}')
        }
      }
    },
    // 退出全屏
    exitFullscreen () {
      if (document.exitFullScreen) {
        document.exitFullScreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    // 监听全屏事件
    fullScreenChange () {
      const isFullScreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement
      if (isFullScreen && isFullScreen.className === 'videoBoxOutter') {
        this.fulling = true
        this.isShowModeFooterWithClick = false
        this.onClickVideoBoxOutter()
      } else if (isFullScreen && isFullScreen.className === 'video-container') {
        this.fulling_single_video = true
      } else {
        this.fulling = false
        this.fulling_single_video = false
        this.single_selected_video = {}
      }
      setTimeout(() => {
        this.resizeVideoBox()
      }, 100);
    },
    // 静音
    mute (val) {
      // 离线 的界面,禁用
      if (val.net <= 0) {
        return
      }
      let el_ref = val.video_ref
      var el = this.$refs[el_ref][0];
      el.volume = 0
      val.volume = 0
    },
    // 取消静音
    unmute (val) {
      // 离线 的界面,禁用
      if (val.net <= 0) {
        return
      }
      let el_ref = val.video_ref
      var el = this.$refs[el_ref][0];
      el.volume = 0.5
      val.volume = 50
    },
    // 调节音量
    dragChangeVolume (val) {
      // 离线 的界面,禁用
      if (val.net <= 0) {
        return
      }
      let el_ref = val.video_ref
      var el = this.$refs[el_ref][0];
      el.volume = val.volume / 100
    },
    // 显示隐藏PTZ控制台
    showPTZ (val, status) {
      this.$set(val, 'show_PTZ_status', status)
    },
    // flv流程
    toPlayFlvVideo (video_index, device_index) {
      // 在组件挂载完成后执行
      if (mpegts.getFeatureList().mseLivePlayback) {
        if (this.video_list[video_index] && !this.video_list[video_index].flv_heartbeat) {
          let _this = this
          this.video_list[video_index].remote_stream_loading = true
          this.sendFlvHeartbeat(video_index, device_index)

          // 10s 视频的状态loading就断开连接,让重试
          this.video_list[video_index].flv_loading_timeout = setTimeout(() => {
            console.log('10s了')
            _this.toStopFlvVideo(video_index)
            _this.video_list[video_index].remote_stream_loading = false
            _this.video_list[video_index].flvPlayer_flag = false
            _this.video_list[video_index].play_state = 2 // 播放失败，重试
          }, this.flv_loading_time)
        }
        this.initFlvPlayer(video_index, device_index); // 初始化 FLV 播放器
      }
    },
    sendFlvHeartbeat (video_index, device_index) {
      let post_data = {
        "act": 1,
        "dp_list": [
          {
            id: 254,
            time: Date.parse(new Date()),
            value: JSON.stringify({ 'cid': this.filter_device_list[device_index].sn })
          }
        ]
      }
      this.$websocket.webSocketSend('pub_dp_act', post_data, this.filter_device_list[device_index].sn)
      let _this = this
      this.video_list[video_index].flv_heartbeat = setTimeout(() => {
        _this.sendFlvHeartbeat(video_index, device_index)
      }, this.flv_heartbeat_time);
    },
    // 初始化 FLV 播放器
    initFlvPlayer (video_index, device_index) {
      var temp_str = 'remoteView' + device_index
      const videoElement = this.$refs[temp_str][0]; // 获取视频元素
      // let video_url = sessionStorage.videoUrl.replace(/%CID%/i, this.filter_device_list[device_index].sn)
      // 解决flv最多只能播放6路的bug:使用多个域名来分散请求，这样可以增加并发数
      let video_url = ''
      // video_url = 'http://flow3.jfgou.com:8091/hls/123456.flv'
      let video_url_index = Math.floor(video_index / 5)
      video_url = this.session_video_url[video_url_index].replace(/%CID%/i, this.filter_device_list[device_index].sn)
      console.log(video_url)

      this.flvPlayer[video_index] = mpegts.createPlayer({
        type: 'mse',  // could also be mpegts, m2ts, flv
        isLive: true,
        url: video_url
      });

      this.flvPlayer[video_index].attachMediaElement(videoElement); //将flv视频装载进video元素内
      this.flvPlayer[video_index].load(); //载入视频
      //播放视频，如果不想要自动播放，去掉本行
      this.flvPlayer[video_index].play().then(() => {
        console.log('开始播放', video_index)
        let el_ref = this.video_list[video_index].video_ref
        let el = this.$refs[el_ref][0];
        el.volume = 0
        // 清掉视频状态loading10秒就销毁的定时器
        if (this.video_list[video_index].flv_loading_timeout) {
          clearTimeout(this.video_list[video_index].flv_loading_timeout)
          this.video_list[video_index].flv_loading_timeout = null;
        }
        this.video_list[video_index].remote_stream_loading = false
        this.video_list[video_index].flvPlayer_flag = true
        this.video_list[video_index].play_state = 1 // 正常播放
        this.$set(this.video_list, video_index, this.video_list[video_index])
      }).catch((error) => {
        console.error(`Player ${video_index} failed to start playing:`, error);
        // this.video_list[video_index].remote_stream_loading = false
        // this.video_list[video_index].flvPlayer_flag = false
        // this.video_list[video_index].play_state = 2 // 播放失败，重试
      });
    },
    toStopFlvVideo (video_index) {
      this.destroyFlvPlayer(video_index);
    },
    // 销毁 FLV 播放器
    destroyFlvPlayer (video_index) {
      if (this.video_list[video_index].flv_heartbeat) {
        clearInterval(this.video_list[video_index].flv_heartbeat);
        this.video_list[video_index].flv_heartbeat = null;
      }
      // 清掉视频状态loading10秒就销毁的定时器
      if (this.video_list[video_index].flv_loading_timeout) {
        clearTimeout(this.video_list[video_index].flv_loading_timeout)
        this.video_list[video_index].flv_loading_timeout = null;
      }
      if (this.flvPlayer[video_index]) {
        this.flvPlayer[video_index].pause(); // 停止播放
        this.flvPlayer[video_index].unload(); // 卸载视频资源
        this.flvPlayer[video_index].detachMediaElement(); // 移除视频元素的绑定
        this.flvPlayer[video_index].destroy(); // 销毁 FLV 播放器实例
        this.flvPlayer[video_index] = null;
      }
    },
    // 重启
    reboot () {
      // let post_data = {
      //   "act": 1,
      //   "dp_list": [
      //     {
      //       id: 572,
      //       time: Date.parse(new Date()),
      //       value: JSON.stringify({ 'int_val': 1 })
      //     }
      //   ]
      // }
      // this.$websocket.webSocketSend('pub_dp_act', post_data, this.reboot_device.sn)
      let post_data = {
        'cid': this.reboot_device.sn,
      }
      this.$websocket.webSocketSend('cli_push_reboot', post_data)
      let video_index = this.reboot_device.video_index
      this.toStopFlvVideo(video_index)
      this.video_list[video_index].remote_stream_loading = false
      this.video_list[video_index].flvPlayer_flag = false
      this.video_list[video_index].play_state = 3 // 设备离线
    },
    // 点击上一页
    changePreviousPage () {
      // 没有设备或者设备数只有一页，点击上下页没有用
      let total_pages = Math.ceil(this.filter_device_list.length / this.multi_screen)
      if (total_pages <= 1) {
        return
      }
      // 要销毁当前页播放的
      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        if (this.filter_device_list[i]) {
          this.$set(this.filter_device_list[i], 'selected_device_flag', false)
          this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
          this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
        }
      }
      this.video_list = [].concat(Array(this.multi_screen).fill({}))
      this.single_selected_device = {}
      // 如果当前是第一页，点击上一页就切换到最后一页
      if (this.page_num === 1) {
        this.page_num = total_pages
      } else {
        this.page_num -= 1
      }

      // 检查轮播
      this.checkCarouselSwitch()

      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        // 准备播放的视频
        if (this.filter_device_list[i]) {
          this.filter_device_list[i].selected_device_flag = true
          this.filter_device_list[i].selected_single_device_flag = false
        }
        let temp_obj = {
          channel_name: this.filter_device_list[i] ? this.filter_device_list[i].sn : '',
          video_ref: "remoteView" + i,
          video_con_ref: "videoContainer" + i,
          video_index: i - (this.page_num - 1) * this.multi_screen,
          live_stream: false,
          volume: 0,
          show_PTZ_status: false,
          show_headwrap_operation: false,
          remote_stream_loading: false
        }
        if (this.filter_device_list[i] && this.filter_device_list[i].net <= 0) {
          temp_obj.flv_heartbeat = null // 播放的心跳
          temp_obj.play_state = 3 // 设备离线
        }
        Object.assign(temp_obj, this.filter_device_list[i])
        this.$set(this.video_list, i - (this.page_num - 1) * this.multi_screen, temp_obj)
        console.log(this.video_list)
        if (this.filter_device_list[i] && this.filter_device_list[i].net > 0) {
          this.$nextTick(() => {
            this.toPlayFlvVideo(i - (this.page_num - 1) * this.multi_screen, i)
          })
        }
      }
    },
    // 点击下一页
    changeNextPage () {
      // 没有设备或者设备数只有一页，点击上下页没有用
      let total_pages = Math.ceil(this.filter_device_list.length / this.multi_screen)
      if (total_pages <= 1) {
        return
      }
      // 要销毁当前页播放的
      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        if (this.filter_device_list[i]) {
          this.$set(this.filter_device_list[i], 'selected_device_flag', false)
          this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
          this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
        }
      }
      this.video_list = [].concat(Array(this.multi_screen).fill({}))
      this.single_selected_device = {}
      // 如果当前是最后一页，点击下一页就切换到第一页
      if (this.page_num === total_pages) {
        this.page_num = 1
      } else {
        this.page_num += 1
      }

      // 检查轮播
      this.checkCarouselSwitch()

      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        // 准备播放的视频
        if (this.filter_device_list[i]) {
          this.filter_device_list[i].selected_device_flag = true
          this.filter_device_list[i].selected_single_device_flag = false
        }
        let temp_obj = {
          channel_name: this.filter_device_list[i] ? this.filter_device_list[i].sn : '',
          video_ref: "remoteView" + i,
          video_con_ref: "videoContainer" + i,
          video_index: i - (this.page_num - 1) * this.multi_screen,
          live_stream: false,
          volume: 0,
          show_PTZ_status: false,
          show_headwrap_operation: false,
          remote_stream_loading: false
        }
        if (this.filter_device_list[i] && this.filter_device_list[i].net <= 0) {
          temp_obj.flv_heartbeat = null // 播放的心跳
          temp_obj.play_state = 3 // 设备离线
        }
        Object.assign(temp_obj, this.filter_device_list[i])
        this.$set(this.video_list, i - (this.page_num - 1) * this.multi_screen, temp_obj)
        if (this.filter_device_list[i] && this.filter_device_list[i].net > 0) {
          this.$nextTick(() => {
            this.toPlayFlvVideo(i - (this.page_num - 1) * this.multi_screen, i)
          })
        }
      }
      console.log(this.video_list)
    },
    // 检查轮播开关
    checkCarouselSwitch () {
      console.log('checkCarouselSwitch')
      if (this.carousel_switch) {
        this.startCarousel()
      } else {
        this.endCarousel()
      }
    },
    // 开始轮播
    startCarousel () {
      // 先判断之前有没有正在轮播的，有就停下，重新开始
      if (this.carousel_interval) {
        clearInterval(this.carousel_interval);
        this.carousel_interval = null;
      }
      // 没有设备或者设备数只有一页，轮播没有用
      let total_pages = Math.ceil(this.filter_device_list.length / this.multi_screen)
      if (total_pages <= 1) {
        return
      } else {
        let _this = this
        this.carousel_interval = setInterval(function () {
          _this.changeNextPage()
        }, this.carousel_time);
      }
    },
    // 关轮播定时器
    endCarousel () {
      if (this.carousel_interval) {
        clearInterval(this.carousel_interval);
        this.carousel_interval = null;
      }
      this.carousel_switch = false
    },
    // 显示提示弹窗
    showDialog (type, val) {
      this.dialog_type = type
      // 刷新分组
      if (type === 'refresh') {
        this.dialog_title = this.$t('refreshList')
        this.dialog_content = this.$t('refreshListTipCon')
        this.dialog_tip = this.$t('refreshListTipCon1')
      } else if (type === 'reboot') { // 重启设备
        if (val.net <= 0) {
          this.$message.error(this.$t('deviceOffline'));
          return
        }
        this.dialog_title = this.$t('rebootDevice')
        this.dialog_content = this.$t('rebootDeviceTipCon')
        this.dialog_tip = ''
        this.reboot_device = val
      }
      this.dialog_visible = true

      // 解决全屏弹窗不显示的问题
      this.$nextTick(() => {
        const videoBoxElement = this.$refs['videoBoxOutter']
        const tipDialogElement = this.$el.querySelector('.tipDialog');

        if (this.fulling) {
          videoBoxElement.appendChild(tipDialogElement)
        } else if (this.fulling_single_video) {
          let single_selected_video_el = this.single_selected_video
          single_selected_video_el.appendChild(tipDialogElement)
        }
      })
    },
    closeDialog () {
      this.dialog_visible = false
      this.dialogSaveVideo_visible = false
      this.submit_loading = false
      this.isSubmitting = false
      this.reboot_device = {}
    },
    submitDialog () {
      this.submit_loading = true
      // 刷新分组
      if (this.dialog_type === 'refresh') {
        this.getDeviceList('refresh')
        this.closeDialog()
      } else if (this.dialog_type === 'reboot') { // 重启设备
        this.reboot()
        this.closeDialog()
      }
    },

    // 当点击视频区域总盒子
    onClickVideoBoxOutter () {
      if (this.fulling) {
        this.isShowModeFooterWithClick = !this.isShowModeFooterWithClick

        if (this.isShowModeFooterWithClick) {
          if (clickVideoBoxOutterResumeTimeout) clearTimeout(clickVideoBoxOutterResumeTimeout)
          clickVideoBoxOutterResumeTimeout = setTimeout(() => {
            this.isShowModeFooterWithClick = false
          }, 3000);
        }
      }
    },

    // 当鼠标经过FooterHolder，展示Footer
    onMouseEnterFooterHolder () {
      this.isShowModeFooterWithMouse = true
    },
    ouMouseLiveFooter () {
      this.isShowModeFooterWithMouse = false
      this.isShowModeFooterWithClick = false
      clearTimeout(clickVideoBoxOutterResumeTimeout)
    },
    // 当鼠标经过perv和next按钮,改变显示
    onHoverPrev (val) {
      this.isHoveringPrev = val
    },
    onHoverNext (val) {
      this.isHoveringNext = val
    },
    // 鼠标悬停出现视频标题状态栏和视频操作栏
    onMouseEnterVideoItem (val) {
      this.$set(val, 'show_headwrap_operation', true)
    },
    ouMouseLeaveVideoItem (val) {
      this.$set(val, 'show_headwrap_operation', false)
    },
    // 点击选中某一个设备
    selectSingleDevice (device_obj) {
      // 1.如果开启了轮播按钮，点击吐司框提示“轮播状态不能点击设备”
      if (this.carousel_switch) {
        this.$message.warning(this.$t('clickSingleDeviceTip'))
        return
      }
      // 如果没开启轮播
      // 2.画面是1路设备，点击后列表该设备有点击的效果，右方监控画面切换到该设备，监控画面有点击的效果。再次点击列表该设备或监控画面，取消点击的效果。
      // 3.画面是4/9/16/25路设备，列表会先将每个设备排序分配到对应4/9/16/25的分组中，点击后选中该设备所在的分组，监控画面切换到该设备分组的设备监控画面，该设备有点击效果，该设备的监控画面有点击效果。再次点击列表该设备或监控画面，取消点击的效果。

      // 点击取消选中
      if (this.single_selected_device.sn && this.single_selected_device.sn === device_obj.sn) {

      } else if (this.single_selected_device.sn && this.single_selected_device.sn !== device_obj.sn) { // 之前有选中的设备，但是现在点击选中的是其他的
        this.filter_device_list.forEach((item) => {
          this.$set(item, 'selected_single_device_flag', false);
        });
        this.video_list.forEach((item) => {
          this.$set(item, 'selected_single_device_flag', false);
        });
      } else { // 之前没有选中的设备，点击选中

      }
      this.single_selected_device = device_obj

      let device_video_obj = this.video_list.find(v => v.sn === device_obj.sn)
      // 如果点击的设备是在播放列表里，只需要改点击样式即可
      if (device_video_obj) {

      } else { // 如果点击的设备不在播放列表里，需要跳到有选中设备的那一页，进行播放，加上点击样式
        this.jumpPage(Math.ceil((device_obj.device_index + 1) / this.multi_screen))
      }
      this.$nextTick(() => {
        let status = device_obj.selected_single_device_flag
        this.$set(device_obj, 'selected_single_device_flag', !status)
        if (!device_video_obj) {
          device_video_obj = this.video_list.find(v => v.sn === device_obj.sn)
        }
        this.$set(device_video_obj, 'selected_single_device_flag', !status)
      })
    },
    // 点击视频播放框，取消选中
    handleSelectedSingleDevice (video_obj) {
      if (video_obj.selected_single_device_flag) {
        this.$set(video_obj, 'selected_single_device_flag', false)
        this.$set(this.filter_device_list[video_obj.device_index], 'selected_single_device_flag', false)
        this.single_selected_device = {}
      }
    },
    // 跳转到指定页播放
    jumpPage (page_num) {
      // 要销毁当前页播放的
      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        if (this.filter_device_list[i]) {
          this.$set(this.filter_device_list[i], 'selected_device_flag', false)
          this.$set(this.filter_device_list[i], 'selected_single_device_flag', false)
          this.toStopFlvVideo(i - (this.page_num - 1) * this.multi_screen) // 销毁 FLV 播放器
        }
      }
      this.video_list = [].concat(Array(this.multi_screen).fill({}))
      // 跳转到指定页
      this.page_num = page_num

      // 检查轮播
      this.checkCarouselSwitch()

      for (let i = (this.page_num - 1) * this.multi_screen; i < this.page_num * this.multi_screen; i++) {
        // 准备播放的视频
        if (this.filter_device_list[i]) {
          this.filter_device_list[i].selected_device_flag = true
          this.filter_device_list[i].selected_single_device_flag = false
        }
        let temp_obj = {
          channel_name: this.filter_device_list[i] ? this.filter_device_list[i].sn : '',
          video_ref: "remoteView" + i,
          video_con_ref: "videoContainer" + i,
          video_index: i - (this.page_num - 1) * this.multi_screen,
          live_stream: false,
          volume: 0,
          show_PTZ_status: false,
          show_headwrap_operation: false,
          remote_stream_loading: false
        }
        if (this.filter_device_list[i] && this.filter_device_list[i].net <= 0) {
          temp_obj.flv_heartbeat = null // 播放的心跳
          temp_obj.play_state = 3 // 设备离线
        }
        Object.assign(temp_obj, this.filter_device_list[i])
        this.$set(this.video_list, i - (this.page_num - 1) * this.multi_screen, temp_obj)
        if (this.filter_device_list[i] && this.filter_device_list[i].net > 0) {
          this.$nextTick(() => {
            this.toPlayFlvVideo(i - (this.page_num - 1) * this.multi_screen, i)
          })
        }
      }
      console.log(this.video_list)
    },
    // 全屏单个视频
    fullSingleScreen (video_obj) {
      /* 全屏指定元素的代码 */
      const videoBoxElement = this.$refs[video_obj.video_con_ref][0]
      this.single_selected_video = videoBoxElement
      if (videoBoxElement.requestFullscreen) {
        videoBoxElement.requestFullscreen();
      } else if (element.mozRequestFullScreen) { // Firefox
        videoBoxElement.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) { // Chrome, Safari, and Opera
        videoBoxElement.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) { // IE/Edge
        videoBoxElement.msRequestFullscreen();
      }
    },
    // 弹出保存视频的弹窗
    showSaveVideoPop (item) {
      // if (item.net <= 0) {
      //   this.$message.error(this.$t('deviceOffline'));
      //   return
      // }

      this.dialogSaveVideo_visible = true

      this.saveVideoSn = item.sn

      // 解决全屏弹窗不显示的问题
      this.$nextTick(() => {
        const videoBoxElement = this.$refs['videoBoxOutter']
        const tipDialogElement = this.$el.querySelector('.saveVideoDialog');

        if (this.fulling) {
          videoBoxElement.appendChild(tipDialogElement)
        } else if (this.fulling_single_video) {
          let single_selected_video_el = this.single_selected_video
          single_selected_video_el.appendChild(tipDialogElement)
        }
      })
    },
    //提交保存视频
    submitSaveVideo () {
      if (!this.saveVideoEndTime || !this.saveVideoBeginTime) {
        this.$message.error(this.$t('pleaseSelect'))
        return
      }

      this.isSubmitting = true

      let beginTime = new Date(this.saveVideoBeginTime)
      beginTime.setSeconds(0)
      beginTime.setMilliseconds(0)
      let endTime = new Date(this.saveVideoEndTime)
      endTime.setSeconds(0)
      endTime.setMilliseconds(0)

      getDeviceVideo({
        cid: this.saveVideoSn,
        start_time: Math.round(beginTime.valueOf() / 1000),
        end_time: Math.round(endTime.valueOf() / 1000)
      }).then(res => {
        if (res.code === 200) {
          this.$message.success(this.$t('saveVideoSuccess'))
          this.closeDialog()
        }
      }).finally(() => {
        this.isSubmitting = false
      })
    },
    onBeginTimeChange(val) {
      this.saveVideoBeginTime = val
    },
    onEndTimeChange(val) {
      this.saveVideoEndTime = val
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.webRTCVideo {
  padding: 24px 20px 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  gap: 30px;
  background: var(--color-neutral-700);
}
.deviceBox {
  flex: 1;
  height: calc(100vh - 114px);
  background: var(--color-neutral-600);
  border-radius: 6px;
}
.deviceGroupBox {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid var(--color-neutral-500);
}
.selectGroupBox {
  font-size: var(--font-size-regular);
  color: var(--color-neutral-200);
}
.selectedGroupName {
  display: flex;
  align-items: center;
  word-break: break-all;
  line-height: 20px;
}
.bottomArrowBox {
  display: inline-block;
  width: 22px;
  height: 22px;
  background: var(--color-neutral-500);
  border: 1px solid var(--color-neutral-500);
  border-radius: 24px;
  margin-left: 8px;
  &:hover {
    background: var(--color-neutral-600);
    border: 1px solid var(--color-neutral-700);
  }
}
.bottomArrowIcon {
  width: 18px;
  height: 18px;
  margin: 2px;
}
.loadImg {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  background: url("../assets/img/loadImg.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/loadImgHover.png");
    background-size: 100% 100%;
  }
}
.deviceListCon {
  height: calc(100% - 91px);
  overflow: auto;
  position: relative;
}
.deviceLoadingBox,
.noDeviceBox {
  font-size: var(--font-size-small);
  color: var(--color-neutral-400);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  .noDeviceIcon {
    width: 60px;
    height: 60px;
  }
}
.loadingIcon {
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.deviceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-neutral-500);
  font-size: var(--font-size-regular);
  color: var(--color-neutral-200);
  .deviceItemLeft {
    width: calc(100% - 10px);
    display: flex;
    align-items: center;
    word-break: break-all;
    line-height: 22px;
    .deviceIcon {
      width: 36px;
      margin-right: 12px;
    }
  }
}
.selectedDeviceVideo {
  background: var(--color-neutral-500);
  border-left: 4px solid var(--color-primary);
  padding-left: 12px;
  color: var(--color-neutral-100);
}
.selectedSingleDeviceVideo {
  background: var(--color-neutral-700);
  color: var(--color-primary);
}
// 实时视频样式
.playVideoBox {
  flex: 4.35;
  height: calc(100vh - 115px);
}
.liveModeBox {
  height: 100%;
}
.videoBoxOutter {
  height: 100%;
  position: relative;
}
.videoBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  gap: 12px;
  height: calc(100% - 64px);
  min-height: 356px;
  // aspect-ratio: 16/9;
  // max-width: 1100px;
  margin: 0 auto;
}
.videoBox.fullingBox {
  height: 100%;
  width: auto;
  min-height: none;
  max-width: none;
  padding: 0;
}
.videoItem {
  width: 100%;
  // height: 100%;
}
.selectedSingleDeviceVideoBox {
  box-shadow: 0px 0px 0px 3px var(--color-primary);
}
.video-container {
  background: var(--color-black) url("../assets/img/noDataLogo.png") 50% 50%
    no-repeat;
  background-size: auto 50%;
  display: inline-block;
  width: 100%;
  height: 100% !important;
  min-height: 109px;
  // height: calc(100% - 1px);
  // aspect-ratio: 16/9;
  position: relative;
  .videoLoadingBox,
  .videoFailBox {
    width: 100%;
    height: 100%;
    background: var(--color-black);
    font-size: var(--font-size-small);
    color: var(--color-neutral-200);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    line-height: 17px;
    gap: 8px;
    .playFailIcon {
      width: 40px;
      height: 40px;
    }
    .retryBtn {
      margin-top: 2px;
      color: var(--color-primary);
    }
  }
}
video {
  width: 100%;
  height: 100%;
  background: var(--color-black);
}
// 历史视频加暂停按钮，所以把控制栏显示出来，去掉进度条，音量，全屏等按钮
//进度条
video::-webkit-media-controls-timeline {
  display: none;
}
//音量按钮
video::-webkit-media-controls-mute-button {
  display: none;
}
video::-webkit-media-controls-toggle-closed-captions-button {
  display: none;
}
//音量的控制条
video::-webkit-media-controls-volume-slider {
  display: none;
}
//全屏按钮
video::-webkit-media-controls-fullscreen-button {
  display: none;
}
.videoWrapHead {
  width: 100%;
  position: absolute;
  top: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  word-break: break-all;
  .videoWrapHeadLeft {
    display: flex;
    align-items: center;
  }
  .videoWrapHeadRight {
    display: flex;
    flex-wrap: nowrap;
    gap: 2px;
  }
  .deviceName {
    color: var(--color-neutral-100);
  }
  .deviceNetStatus {
    font-size: var(--font-size-small);
  }
  .deviceOnline {
    color: var(--color-warning-400);
  }
  .deviceOffline {
    color: var(--color-negative-400);
  }
  .saveVideoIcon {
    flex-shrink: 0;
    background: url("../assets/img/saveVideo.png");
    background-size: 100% 100%;
    &:hover {
      background: url("../assets/img/saveVideoHover.png");
      background-size: 100% 100%;
    }
  }
  .stopVideoImg {
    flex-shrink: 0;
    background: url("../assets/img/power.png");
    background-size: 100% 100%;
    &:hover {
      background: url("../assets/img/powerHover.png");
      background-size: 100% 100%;
    }
  }
}
.videoWrapDirection {
  width: 100%;
  height: 100%;
  background: url("../assets/img/directionBg.png") no-repeat;
  background-size: 100% 100%;
  transform: rotate(-45deg);
}
.directionBtnBox {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  cursor: pointer;
}
.directionBtn {
  width: 50%;
  height: 50%;
  position: relative;
  overflow: hidden;
  &:hover,
  &:active,
  &:focus {
    background: url("../assets/img/directionActive.png") no-repeat;
    background-size: 100% 100%;
  }
}
.directionLeftBtn {
  transform: rotate(180deg);
}
.directionUpBtn {
  transform: rotate(270deg);
}
.directionDownBtn {
  transform: rotate(90deg);
}
.cameraControl {
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.noVolumeIcon {
  background: url("../assets/img/noVolume.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/noVolumeHover.png");
    background-size: 100% 100%;
  }
}
.hasVolumeIcon {
  background: url("../assets/img/hasVolume.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/hasVolumeHover.png");
    background-size: 100% 100%;
  }
}
.sliderStyle {
  display: inline-block;
}
.showPtzIcon {
  background: url("../assets/img/ptz.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/ptzClick.png");
    background-size: 100% 100%;
  }
}
/** 单个视频画面全屏 **/
.exitFullSingleScreenIcon {
  width: 100%;
  height: 100%;
  background: url("../assets/img/zoomOut.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/zoomOutHover.png");
    background-size: 100% 100%;
  }
}
.fullScreenSingleIcon {
  width: 100%;
  height: 100%;
  background: url("../assets/img/zoomIn.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/zoomInHover.png");
    background-size: 100% 100%;
  }
}
// 1路的不同样式
.videoItem1 {
  width: 100%;
  height: 100%;
  .videoWrapHead {
    width: calc(100% - 32px);
    padding: 0 16px;
    .deviceName {
      font-size: var(--font-size-regular);
      line-height: 22px;
      margin-right: 10px;
    }
    .stopVideoImg,
    .saveVideoIcon {
      width: 40px;
      height: 40px;
    }
  }
  .cameraControl {
    right: 20px;
    bottom: 20px;
    padding: 0 10px;
    .volumeIcon,
    .ptzIcon,
    .fullingSingleVideoBox {
      width: 40px;
      height: 40px;
    }
    .sliderStyle {
      width: 110px;
      margin: 0 10px 0 2px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 4px;
    }
    /deep/ .el-slider__button {
      width: 14px;
      height: 14px;
      background: var(--color-neutral-100);
      border-radius: 50%;
      top: -1px;
    }
  }
  .showPtzBox {
    width: 130px;
    height: 130px;
    position: absolute;
    bottom: 80px;
    right: 20px;
  }
}
// 4路的不同样式
.videoItem4 {
  width: calc(50% - 6px);
  height: calc(50% - 6px);
  .videoWrapHead {
    width: calc(100% - 32px);
    padding: 0 16px;
    .deviceName {
      font-size: var(--font-size-regular);
      line-height: 22px;
      margin-right: 10px;
    }
    .stopVideoImg,
    .saveVideoIcon {
      width: 40px;
      height: 40px;
    }
  }
  .cameraControl {
    right: 12px;
    bottom: 12px;
    padding: 0 10px;
    .volumeIcon,
    .ptzIcon,
    .fullingSingleVideoBox {
      width: 40px;
      height: 40px;
    }
    .sliderStyle {
      width: 110px;
      margin: 0 10px 0 2px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 4px;
    }
    /deep/ .el-slider__button {
      width: 14px;
      height: 14px;
      background: var(--color-neutral-100);
      border-radius: 50%;
      top: -1px;
    }
  }
  .showPtzBox {
    width: 110px;
    height: 110px;
    position: absolute;
    bottom: 64px;
    right: 12px;
  }
}
// 9路的不同样式
.videoItem9 {
  width: calc(33.33% - 8px);
  height: calc(33.33% - 8px);
  .videoWrapHead {
    width: calc(100% - 24px);
    padding: 0 12px;
    .deviceName {
      font-size: var(--font-size-normal);
      line-height: 20px;
      margin-right: 10px;
    }
    .stopVideoImg,
    .saveVideoIcon {
      width: 34px;
      height: 34px;
    }
  }
  .cameraControl {
    right: 10px;
    bottom: 10px;
    padding: 0 8px;
    .volumeIcon,
    .ptzIcon,
    .fullingSingleVideoBox {
      width: 34px;
      height: 34px;
    }
    .sliderStyle {
      width: 60px;
      margin: 0 8px 0 2px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 3px;
    }
    /deep/ .el-slider__button {
      width: 9px;
      height: 9px;
      background: var(--color-neutral-100);
      border-radius: 50%;
      top: 1px;
    }
  }
  .showPtzBox {
    width: 70px;
    height: 70px;
    position: absolute;
    bottom: 58px;
    right: 12px;
  }
}
// 16路的不同样式
.videoItem16 {
  width: calc(25% - 9px);
  height: calc(25% - 9px);
  .videoWrapHead {
    width: calc(100% - 20px);
    padding: 0 10px;
    .deviceName {
      font-size: var(--font-size-small);
      line-height: 17px;
      margin-right: 6px;
    }
    .stopVideoImg,
    .saveVideoIcon {
      width: 28px;
      height: 28px;
    }
  }
  .cameraControl {
    right: 8px;
    bottom: 8px;
    padding: 0 6px;
    .volumeIcon,
    .ptzIcon,
    .fullingSingleVideoBox {
      width: 28px;
      height: 28px;
    }
    .sliderStyle {
      width: 60px;
      margin: 0 6px 0 2px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 2px;
    }
    /deep/ .el-slider__button {
      width: 6px;
      height: 6px;
      background: var(--color-neutral-100);
      border-radius: 50%;
      top: 2px;
    }
  }
  .showPtzBox {
    width: 52px;
    height: 52px;
    position: absolute;
    bottom: 44px;
    right: 8px;
  }
}
// 25路的不同样式
.videoItem25 {
  width: calc(20% - 10px);
  height: calc(20% - 10px);
  .videoWrapHead {
    width: calc(100% - 16px);
    padding: 0 8px;
    .deviceName {
      font-size: var(--font-size-small);
      line-height: 17px;
      margin-right: 6px;
    }
    .stopVideoImg,
    .saveVideoIcon {
      width: 24px;
      height: 24px;
    }
  }
  .cameraControl {
    right: 6px;
    bottom: 6px;
    padding: 0 4px;
    .volumeIcon,
    .ptzIcon,
    .fullingSingleVideoBox {
      width: 24px;
      height: 24px;
    }
    .sliderStyle {
      width: 60px;
      margin: 0 4px 0 2px;
    }
    /deep/ .el-slider__runway,
    /deep/ .el-slider__bar {
      height: 2px;
    }
    /deep/ .el-slider__button {
      width: 6px;
      height: 6px;
      background: var(--color-neutral-100);
      border-radius: 50%;
      top: 2px;
    }
  }
  .showPtzBox {
    width: 44px;
    height: 44px;
    position: absolute;
    bottom: 36px;
    right: 6px;
  }
}

.rightControlBox {
  cursor: pointer;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-neutral-500);
  &:hover {
    background-color: var(--color-neutral-hover);
  }
}
.rightControlBox.fullingBox {
  background: none;
}
.exitFullScreenIcon {
  width: 100%;
  height: 100%;
  background: url("../assets/img/exitFullScreenIcon.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/exitFullScreenIconHover.png");
    background-size: 100% 100%;
  }
}
.fullScreenIcon {
  width: 100%;
  height: 100%;
  background: url("../assets/img/fullScreenIcon.png");
  background-size: 100% 100%;
  &:hover {
    background: url("../assets/img/fullScreenIconHover.png");
    background-size: 100% 100%;
  }
}
.viderModeFooter {
  display: flex;
  align-items: center;
  padding-top: 20px;
  .pageChangeBox {
    display: flex;
    background-color: var(--color-neutral-500);
    border-radius: 6px;
    overflow: hidden;
    margin-left: 20px;
  }
  .carouselBox {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  .carouselText {
    color: var(--color-neutral-100);
  }
}
.viderModeFooter.fullingBox {
  position: fixed;
  box-sizing: border-box;
  bottom: 0;
  left: 0;
  width: 100vw;
  max-width: none;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.7);
  margin: 0;
}
.modeFooterHolder {
  position: fixed;
  bottom: 0;
  height: 20px;
  width: 100vw;
  left: 0;
}
.multiScreenBox {
  display: flex;
  font-size: 16px;
  color: #819fb5;
  background: var(--color-neutral-600);
  border-radius: 6px;
  overflow: hidden;
}
.multiScreenItem {
  height: 40px;
  padding: 0 14px;
  display: flex;
  align-items: center;
  user-select: none;
  cursor: pointer;
  &:hover {
    background-color: var(--color-neutral-hover);
  }
  img {
    width: 11px;
    height: 21px;
  }
}
.selectedMultiScreen {
  color: var(--color-white);
  background-color: var(--color-primary);
  &:hover {
    color: var(--color-white);
    background-color: var(--color-primary);
  }
}
.dialogCont {
  color: var(--color-neutral-100);
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.tipDialogMask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.7);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;
}
.saveVideoBox {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 500px;
  padding: 0 24px 24px;
  .saveVideoTitle {
    color: var(--color-neutral-100);
  }
  .datePickerBox {
    align-self: center;
  }
}
.el-date-editor .el-range-input {
  width: 200px;
}
.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 500px;
}
</style>
