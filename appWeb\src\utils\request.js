import axios from "axios";
import store from "@/store";
import { removeToken, removeRole } from "@/utils/auth";
import i18n from "@/lang";
import { Message } from "element-ui";

// 创建axios实例
const service = axios.create({
  baseURL: process.env.BASE_API, // api 的 base_url
  timeout: 10000 // request timeout
});

// request拦截器
service.interceptors.request.use(
  config => {
    let reqConfig = { ...config };
    if (reqConfig.data === undefined) reqConfig.data = {};
    reqConfig.data.time = Math.round(new Date().valueOf() / 1000);
    reqConfig.data.auth_token = store.getters.token;
    return reqConfig;
  },
  error => {
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// respone拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code !== 200) {
      var tip = "";
      if (res.code === 401) {
        tip = i18n.t("errCode401");
        setTimeout(() => {
          store.commit("SET_TOKEN", "");
          store.commit("SET_ROLES", []);
          store.commit("SET_ROLE", "");
          sessionStorage.clear();
          removeToken();
          removeRole();
          this.$router.push({ path: "/login" });
        }, 3 * 1000);
      } else if (res.code === 500) {
        tip = i18n.t("errCode500");
      } else if (res.code === 1000 || res.code === 1001) {
        tip = i18n.t("errCode1000");
      } else if (res.code === 1100) tip = i18n.t("errCode1100");
      else if (res.code === 1101) tip = i18n.t("errCode1101");
      else if (res.code === 1102) tip = i18n.t("errCode1102");
      else if (res.code === 1258) tip = i18n.t("userLimitTip");
      else if (res.code === 1260) tip = i18n.t("timerangeexceeds");
      else
      {
        tip = res.msg;
      }
      Message.error(tip);
      return Promise.reject(response.data);
    } else {
      // return response
      return response.data;
    }
  },
  error => {
    console.log("err" + error); // for debug
    const execs = /^(timeout of )(\d+)(ms exceeded)$/g.exec(error.message);
    if (execs && execs.length) {
      error.message = execs[2] ? i18n.t("timeout") : error.message;
    }
    error.message = error.message.replace(
      /(Network Error)/,
      i18n.t("networkError")
    );
    Message.error(error.message);
    return Promise.reject(error);
  }
);

export default service;
